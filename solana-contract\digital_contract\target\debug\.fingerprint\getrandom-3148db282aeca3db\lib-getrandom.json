{"rustc": 8713626761367032038, "features": "[\"custom\", \"js\", \"js-sys\", \"std\", \"wasm-bindgen\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 11884987481660704207, "profile": 10243973527296709326, "path": 9003396575456371556, "deps": [[5682297152023424035, "cfg_if", false, 16456864239769143504]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\getrandom-3148db282aeca3db\\dep-lib-getrandom", "checksum": false}}], "rustflags": [], "metadata": 12606519392706294666, "config": 2202906307356721367, "compile_kind": 0}