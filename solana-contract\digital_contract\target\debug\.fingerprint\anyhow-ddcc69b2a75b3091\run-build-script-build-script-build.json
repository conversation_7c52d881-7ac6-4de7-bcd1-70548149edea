{"rustc": 8713626761367032038, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10291739091677281249, "build_script_build", false, 6807533329815078103]], "local": [{"RerunIfChanged": {"output": "debug\\build\\anyhow-ddcc69b2a75b3091\\output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": [], "metadata": 0, "config": 0, "compile_kind": 0}