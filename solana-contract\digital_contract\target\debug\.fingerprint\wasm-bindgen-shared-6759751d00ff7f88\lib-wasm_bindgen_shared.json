{"rustc": 8713626761367032038, "features": "[]", "declared_features": "[]", "target": 15424970266770562866, "profile": 12174896561884874532, "path": 4394072025573322101, "deps": [[7006636483571730090, "unicode_ident", false, 16102391380354609751], [15771991779085741627, "build_script_build", false, 12769047616847811414]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wasm-bindgen-shared-6759751d00ff7f88\\dep-lib-wasm_bindgen_shared", "checksum": false}}], "rustflags": [], "metadata": 4905702609766663142, "config": 2202906307356721367, "compile_kind": 0}